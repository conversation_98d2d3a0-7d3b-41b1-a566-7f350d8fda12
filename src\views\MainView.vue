<template>
  <div class="main-container">
    <!-- 动态背景元素 -->
    <div class="bg-animation">
      <div class="floating-cross" v-for="n in 8" :key="n" :style="{animationDelay: n * 0.6 + 's'}">+</div>
      <div class="floating-circle" v-for="n in 6" :key="n" :style="{animationDelay: n * 0.8 + 's'}"></div>
      <div class="floating-heart" v-for="n in 4" :key="n" :style="{animationDelay: n * 1.2 + 's'}">♥</div>
    </div>

    <!-- 主页内容 -->
    <div class="content-section">
      <!-- 欢迎卡片 -->
      <div class="welcome-card">
        <!-- <div class="welcome-icon">👋</div> -->
        <h1 class="welcome-title">健康概览</h1>
      </div>

      <!-- 快速操作区域 -->
      <!-- <div class="quick-actions">
        <div class="action-item" @click="navigateTo('/indexView')">
          <div class="action-icon">📊</div>
          <span>查询指标</span>
        </div>
        <div class="action-item" @click="navigateTo('/reportView')">
          <div class="action-icon">📋</div>
          <span>体检报告</span>
        </div>
        <div class="action-item" @click="navigateTo('/examReportView')">
          <div class="action-icon">🧪</div>
          <span>检查报告</span>
        </div>
      </div> -->

      <!-- 医疗数据轮播 -->
      <div class="overview-section" v-if="hasAnyData">
        <div class="swipe-container">
          <!-- 左箭头 -->
          <div class="swipe-arrow swipe-arrow-left" @click="prevSlide">
            <span class="arrow-icon">‹</span>
          </div>

          <!-- 右箭头 -->
          <div class="swipe-arrow swipe-arrow-right" @click="nextSlide">
            <span class="arrow-icon">›</span>
          </div>

          <van-swipe
            ref="swipeRef"
            :autoplay="0"
            :show-indicators="false"
            class="medical-swipe"
            @change="onSwipeChange"
          >
            <!-- 血常规指标 -->
            <van-swipe-item v-if="bloodTestData.length > 0">
              <div class="swipe-content">
                <div class="section-header">
                  <div class="section-title-wrapper">
                    <div class="section-icon">🩸</div>
                    <h3 class="section-title">最新血常规</h3>
                  </div>
                  <div class="check-date">{{ getLatestDate(bloodTestData) }}</div>
                </div>
                <div class="indicators-list">
                  <div
                    v-for="item in bloodTestData"
                    :key="item.index_name"
                    class="indicator-row"
                    @click="goToIndexDetail(item)"
                  >
                    <div class="indicator-info">
                      <div class="indicator-name">{{ item.index_name }}</div>
                      <!-- <div class="indicator-desc">点击查看详情</div> -->
                    </div>
                    <div class="indicator-value-wrapper">
                      <div class="indicator-value" :class="getStatusClass(item.index_status)">
                        {{ item.index_value }}
                        <!-- <span class="indicator-unit">{{ item.index_unit }}</span> -->
                      </div>
                      <!-- <div class="indicator-status">{{ getStatusText(item.index_status) }}</div> -->
                    </div>
                  </div>
                </div>
              </div>
            </van-swipe-item>

            <!-- 肿瘤指标 -->
            <van-swipe-item v-if="tumorMarkersData.length > 0">
              <div class="swipe-content">
                <div class="section-header">
                  <div class="section-title-wrapper">
                    <div class="section-icon">🎯</div>
                    <h3 class="section-title">最新肿瘤指标</h3>
                  </div>
                  <div class="check-date">{{ getLatestDate(tumorMarkersData) }}</div>
                </div>
                <div class="indicators-list">
                  <div
                    v-for="item in tumorMarkersData"
                    :key="item.index_name"
                    class="indicator-row"
                    @click="goToIndexDetail(item)"
                  >
                    <div class="indicator-info">
                      <div class="indicator-name">{{ item.index_name }}</div>
                      <!-- <div class="indicator-desc">点击查看详情</div> -->
                    </div>
                    <div class="indicator-value-wrapper">
                      <div class="indicator-value" :class="getStatusClass(item.index_status)">
                        {{ item.index_value }}
                        <!-- <span class="indicator-unit">{{ item.index_unit }}</span> -->
                      </div>
                      <!-- <div class="indicator-status">{{ getStatusText(item.index_status) }}</div> -->
                    </div>
                  </div>
                </div>
              </div>
            </van-swipe-item>

            <!-- CT报告 -->
            <van-swipe-item v-if="ctReportData.length > 0">
              <div class="swipe-content">
                <div class="section-header">
                  <div class="section-title-wrapper">
                    <div class="section-icon">📋</div>
                    <h3 class="section-title">最新CT报告</h3>
                  </div>
                  <div class="check-date">{{ getLatestDate(ctReportData) }}</div>
                </div>
                <div class="reports-list">
                  <div
                    v-for="report in ctReportData"
                    :key="report.id"
                    class="report-item"
                    @click="goToCTReport(report.id)"
                  >
                    <!-- <div class="report-header">
                      <div class="report-title">{{ report.exam_type || 'CT检查' }}</div>
                      <div class="report-date">{{ formatDate(report.medical_date) }}</div>
                    </div> -->
                    <div class="report-row">
                      <div class="report-label">医院</div>
                      <div class="report-value">{{ report.hospital || '未知医院' }}</div>
                    </div>
                    <div class="report-row" v-if="report.exam_info">
                      <div class="report-label">检查信息</div>
                      <div class="report-value report-summary" @click.stop>
                        <van-text-ellipsis
                          :content="report.exam_info"
                          :rows="5"
                          expand-text="展开"
                          collapse-text="收起"
                        />
                      </div>
                    </div>
                    <div class="report-row" v-if="report.exam_diag">
                      <div class="report-label">诊断结果</div>
                      <div class="report-value report-summary" @click.stop>
                        <van-text-ellipsis
                          :content="report.exam_diag"
                          :rows="5"
                          expand-text="展开"
                          collapse-text="收起"
                        />
                      </div>
                    </div>
                    <div class="report-hint">点击查看完整报告</div>
                  </div>
                </div>
              </div>
            </van-swipe-item>
          </van-swipe>

          <!-- 自定义指示器 -->
          <div class="custom-indicators">
            <div
              v-for="(item, index) in slideItems"
              :key="index"
              class="indicator-dot"
              :class="{ active: currentSlide === index }"
              @click="goToSlide(index)"
            ></div>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <div class="empty-state" v-if="!hasAnyData">
        <div class="empty-icon">📊</div>
        <div class="empty-title">暂无数据</div>
        <div class="empty-subtitle">开始记录您的健康数据吧</div>
        <van-button
          type="primary"
          size="small"
          @click="navigateTo('/indexView')"
          style="margin-top: 16px;"
        >
          查看指标
        </van-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { showToast } from 'vant';
import { getMedicalIndexValue, getMedicalIndex, getMedicalExamLatest } from '@/api/medical';

const router = useRouter();

// 响应式数据
const bloodTestData = ref([]);
const tumorMarkersData = ref([]);
const ctReportData = ref([]);  // 改为数组存储多个CT报告
const loading = ref(false);
const swipeRef = ref(null);
const currentSlide = ref(0);

// 存储指标的完整信息（包含index_id等）
const bloodTestIndexes = ref([]);
const tumorMarkersIndexes = ref([]);


// 计算属性
const hasAnyData = computed(() => {
  return bloodTestData.value.length > 0 ||
         tumorMarkersData.value.length > 0 ||
         ctReportData.value.length > 0;
});

// 计算轮播项目数量
const slideItems = computed(() => {
  const items = [];
  if (bloodTestData.value.length > 0) items.push('blood');
  if (tumorMarkersData.value.length > 0) items.push('tumor');
  if (ctReportData.value.length > 0) items.push('ct');
  return items;
});

// 方法
const navigateTo = (path) => {
  router.push(path);
};

// 修正goToIndexDetail方法，传递正确的参数
const goToIndexDetail = (item) => {
  // 根据指标名称查找完整的指标信息
  let indexInfo = null;

  // 从血常规指标中查找
  if (bloodTestIndexes.value.length > 0) {
    indexInfo = bloodTestIndexes.value.find(index => index.index_name === item.index_name);
  }

  // 从肿瘤指标中查找
  if (!indexInfo && tumorMarkersIndexes.value.length > 0) {
    indexInfo = tumorMarkersIndexes.value.find(index => index.index_name === item.index_name);
  }

  // 如果找到了完整信息，使用完整信息；否则使用基本信息
  if (indexInfo) {
    router.push({
      path: '/indexDetail',
      query: {
        index_id: indexInfo.index_id,
        index_name: indexInfo.index_name,
        is_chart: indexInfo.is_chart || 0,
        reference_max: indexInfo.reference_max || '',
        reference_min: indexInfo.reference_min || 0,
        is_edit: indexInfo.is_edit || 0
      }
    });
  } else {
    // 降级处理：只传递指标名称
    router.push({
      path: '/indexDetail',
      query: {
        index_name: item.index_name,
        is_chart: 1, // 默认显示图表
        reference_max: '',
        reference_min: 0,
        is_edit: 0
      }
    });
  }
};

const goToCTReport = (reportId) => {
  router.push({
    name: 'examReportView',
    query: { reportId: reportId }
  });
};

const formatDate = (date) => {
  if (!date) return '';
  const d = new Date(date);
  return `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(2, '0')}-${String(d.getDate()).padStart(2, '0')}`;
};

const getStatusClass = (status) => {
  return {
    'status-normal': status === 'normal',
    'status-high': status === 'high',
    'status-low': status === 'low',
    'status-abnormal': status === 'abnormal'
  };
};

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    'normal': '正常',
    'high': '偏高',
    'low': '偏低',
    'abnormal': '异常'
  };
  return statusMap[status] || '未知';
};

// 轮播控制方法
const prevSlide = () => {
  if (swipeRef.value) {
    swipeRef.value.prev();
  }
};

const nextSlide = () => {
  if (swipeRef.value) {
    swipeRef.value.next();
  }
};

const goToSlide = (index) => {
  if (swipeRef.value) {
    swipeRef.value.swipeTo(index);
  }
};

const onSwipeChange = (index) => {
  currentSlide.value = index;
};

// 获取最新检查日期
const getLatestDate = (dataArray) => {
  if (!dataArray || dataArray.length === 0) return '';
  const latestItem = dataArray.reduce((latest, current) => {
    return new Date(current.medical_date) > new Date(latest.medical_date) ? current : latest;
  });
  return formatDate(latestItem.medical_date);
};

// 获取最新血常规数据
const fetchBloodTestData = async () => {
  try {
    // 同时获取指标值和指标信息
    const [valueResponse, indexResponse] = await Promise.all([
      getMedicalIndexValue({
        medical_type: 2,
        limit: 6
      }),
      getMedicalIndex({
        medical_type: 2
      })
    ]);

    if (valueResponse.data.data && Array.isArray(valueResponse.data.data)) {
      bloodTestData.value = valueResponse.data.data;
    }

    if (indexResponse.data && Array.isArray(indexResponse.data)) {
      bloodTestIndexes.value = indexResponse.data;
    }
  } catch (error) {
    console.error('获取血常规数据失败:', error);
  }
};

// 获取最新肿瘤指标数据
const fetchTumorMarkersData = async () => {
  try {
    // 同时获取指标值和指标信息
    const [valueResponse, indexResponse] = await Promise.all([
      getMedicalIndexValue({
        medical_type: 4,
        limit: 5
      }),
      getMedicalIndex({
        medical_type: 4
      })
    ]);

    if (valueResponse.data.data && Array.isArray(valueResponse.data.data)) {
      tumorMarkersData.value = valueResponse.data.data;
    }

    if (indexResponse.data && Array.isArray(indexResponse.data)) {
      tumorMarkersIndexes.value = indexResponse.data;
    }
  } catch (error) {
    console.error('获取肿瘤指标数据失败:', error);
  }
};

// 获取最新CT报告数据
const fetchCTReportData = async () => {
  try {
    const response = await getMedicalExamLatest();
    if (response.data && response.data.data && Array.isArray(response.data.data)) {
      ctReportData.value = response.data.data;
    }
  } catch (error) {
    console.error('获取CT报告数据失败:', error);
  }
};

// 初始化数据
const initData = async () => {
  loading.value = true;
  try {
    await Promise.all([
      fetchBloodTestData(),
      fetchTumorMarkersData(),
      fetchCTReportData()
    ]);
  } catch (error) {
    showToast('加载数据失败');
  } finally {
    loading.value = false;
  }
};

onMounted(() => {
  initData();
});
</script>

<style scoped>
.main-container {
  min-height: calc(100vh - 50px); /* 减去底部导航栏高度 */
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
  position: relative;
  overflow-x: hidden;
  padding-bottom: 80px; /* 增加底部空间，确保不被导航栏遮挡 */
  box-sizing: border-box;
}

/* 动态背景元素样式 - 与HomeView保持一致 */
.bg-animation {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.floating-cross {
  position: absolute;
  color: rgba(255, 255, 255, 0.08);
  font-size: 20px;
  font-weight: bold;
  animation: float 8s ease-in-out infinite;
}

.floating-cross:nth-child(1) { top: 5%; left: 8%; }
.floating-cross:nth-child(2) { top: 15%; right: 12%; }
.floating-cross:nth-child(3) { top: 45%; left: 3%; }
.floating-cross:nth-child(4) { top: 65%; right: 8%; }
.floating-cross:nth-child(5) { top: 25%; left: 85%; }
.floating-cross:nth-child(6) { bottom: 35%; left: 15%; }
.floating-cross:nth-child(7) { bottom: 15%; right: 20%; }
.floating-cross:nth-child(8) { top: 80%; left: 70%; }

.floating-circle {
  position: absolute;
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.08);
  border-radius: 50%;
  animation: pulse 6s ease-in-out infinite;
}

.floating-circle:nth-child(9) { top: 12%; left: 50%; }
.floating-circle:nth-child(10) { top: 55%; right: 25%; }
.floating-circle:nth-child(11) { bottom: 25%; left: 60%; }
.floating-circle:nth-child(12) { top: 35%; right: 5%; }
.floating-circle:nth-child(13) { bottom: 45%; left: 35%; }
.floating-circle:nth-child(14) { top: 75%; right: 45%; }

.floating-heart {
  position: absolute;
  color: rgba(255, 255, 255, 0.06);
  font-size: 18px;
  animation: heartbeat 4s ease-in-out infinite;
}

.floating-heart:nth-child(15) { top: 30%; left: 20%; }
.floating-heart:nth-child(16) { top: 60%; right: 15%; }
.floating-heart:nth-child(17) { bottom: 20%; left: 80%; }
.floating-heart:nth-child(18) { top: 85%; left: 40%; }

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-15px) rotate(180deg); }
}

@keyframes pulse {
  0%, 100% { transform: scale(1); opacity: 0.08; }
  50% { transform: scale(1.3); opacity: 0.15; }
}

@keyframes heartbeat {
  0%, 100% { transform: scale(1); }
  25% { transform: scale(1.1); }
  50% { transform: scale(1); }
  75% { transform: scale(1.05); }
}

/* 内容区域 */
.content-section {
  position: relative;
  z-index: 2;
  padding: 0 16px;
  padding-bottom: 100px; /* 增加底部间距，避免遮挡导航栏 */
}

/* 欢迎卡片 */
.welcome-card {
  background: rgba(255, 255, 255, 0.95);
  padding: 24px;
  border-radius: 20px;
  text-align: center;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  margin-bottom: 20px;
}

.welcome-icon {
  font-size: 32px;
  margin-bottom: 12px;
}

.welcome-title {
  color: #2c5aa0;
  font-size: 24px;
  font-weight: 700;
  margin: 8px 0;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.welcome-subtitle {
  color: #6b7280;
  font-size: 14px;
  margin: 0;
}

/* 快速操作区域 */
.quick-actions {
  display: flex;
  justify-content: space-around;
  background: rgba(255, 255, 255, 0.95);
  padding: 20px;
  border-radius: 16px;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  margin-bottom: 20px;
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  transition: transform 0.2s ease;
  padding: 8px;
  border-radius: 12px;
}

.action-item:hover {
  transform: translateY(-2px);
  background: rgba(44, 90, 160, 0.1);
}

.action-icon {
  font-size: 24px;
  margin-bottom: 8px;
}

.action-item span {
  color: #374151;
  font-size: 12px;
  font-weight: 500;
}

/* 概览区域 */
.overview-section {
  background: rgba(255, 255, 255, 0.95);
  padding: 20px;
  border-radius: 16px;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  margin-bottom: 16px;
}

/* 轮播容器 */
.swipe-container {
  position: relative;
}

/* 轮播箭头 */
.swipe-arrow {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 36px;
  height: 36px;
  background: rgba(44, 90, 160, 0.8);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 10;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.swipe-arrow:hover {
  background: rgba(44, 90, 160, 0.9);
  transform: translateY(-50%) scale(1.1);
}

.swipe-arrow-left {
  left: -18px;
}

.swipe-arrow-right {
  right: -18px;
}

.swipe-arrow .arrow-icon {
  color: white;
  font-size: 20px;
  font-weight: bold;
  line-height: 1;
}

/* 自定义指示器 */
.custom-indicators {
  display: flex;
  justify-content: center;
  gap: 8px;
  margin-top: 16px;
}

.indicator-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: rgba(44, 90, 160, 0.3);
  cursor: pointer;
  transition: all 0.3s ease;
}

.indicator-dot.active {
  background: #2c5aa0;
  transform: scale(1.2);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 2px solid rgba(44, 90, 160, 0.1);
}

.section-title-wrapper {
  display: flex;
  align-items: center;
  gap: 8px;
}

.section-icon {
  font-size: 20px;
}

.section-title {
  color: #2c5aa0;
  font-size: 18px;
  font-weight: 700;
  margin: 0;
}

/* 轮播样式 */
.medical-swipe {
  border-radius: 16px;
  overflow: hidden;
}

.swipe-content {
  background: #f8fafc;
  /* padding: 20px; */
  min-height: 200px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.check-date {
  color: #6b7280;
  font-size: 12px;
  background: rgba(255, 255, 255, 0.8);
  padding: 4px 8px;
  border-radius: 8px;
}

/* 指标列表样式 */
.indicators-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.indicator-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: rgba(255, 255, 255, 0.8);
  padding: 16px;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid rgba(44, 90, 160, 0.1);
  /* margin-bottom: 8px; */
}

.indicator-row:hover {
  background: rgba(255, 255, 255, 0.95);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(44, 90, 160, 0.15);
}

.indicator-info {
  flex: 1;
}

.indicator-name {
  color: #2c5aa0;
  font-size: 15px;
  font-weight: 600;
  margin-bottom: 4px;
}

.indicator-desc {
  color: #6b7280;
  font-size: 12px;
  font-weight: 400;
}

.indicator-value-wrapper {
  text-align: right;
}

.indicator-value {
  font-size: 16px;
  font-weight: 700;
  margin-bottom: 2px;
}

.indicator-unit {
  font-size: 12px;
  font-weight: 400;
  margin-left: 4px;
}

.indicator-status {
  font-size: 11px;
  font-weight: 500;
  opacity: 0.8;
}

/* 状态颜色 */
.status-normal {
  color: #10b981;
}

.status-high {
  color: #ef4444;
}

.status-low {
  color: #f59e0b;
}

.status-abnormal {
  color: #ef4444;
}

/* 报告内容样式 */
.report-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.report-content:hover {
  transform: translateY(-2px);
}

/* 报告列表样式 */
.reports-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
  max-height: 400px;
  overflow-y: auto;
}

.report-item {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 12px;
  /* padding: 16px; */
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid rgba(44, 90, 160, 0.1);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.report-item:hover {
  background: rgba(255, 255, 255, 1);
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(44, 90, 160, 0.15);
}

.report-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid rgba(44, 90, 160, 0.1);
}

.report-title {
  color: #2c5aa0;
  font-size: 16px;
  font-weight: 600;
}

.report-date {
  color: #6b7280;
  font-size: 12px;
  background: rgba(44, 90, 160, 0.1);
  padding: 4px 8px;
  border-radius: 6px;
}

.report-row {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  background: rgba(255, 255, 255, 0.8);
  padding: 14px 16px;
  border-radius: 10px;
  transition: all 0.2s ease;
  border: 1px solid rgba(44, 90, 160, 0.1);
}

.report-row:hover {
  background: rgba(255, 255, 255, 0.95);
  box-shadow: 0 2px 8px rgba(44, 90, 160, 0.1);
}

.report-label {
  color: #374151;
  font-size: 14px;
  font-weight: 600;
  min-width: 80px;
  flex-shrink: 0;
}

.report-value {
  color: #2c5aa0;
  font-size: 14px;
  font-weight: 600;
  text-align: right;
  flex: 1;
}

.report-summary {
  text-align: left;
  color: #6b7280;
  font-weight: 400;
  line-height: 1.4;
}

.report-hint {
  text-align: center;
  color: #6b7280;
  font-size: 12px;
  font-style: italic;
  margin-top: 8px;
  padding: 8px;
  background: rgba(44, 90, 160, 0.05);
  border-radius: 6px;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 40px 20px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.6;
}

.empty-title {
  color: #374151;
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 8px;
}

.empty-subtitle {
  color: #6b7280;
  font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .swipe-content {
    /* padding: 16px; */
    min-height: 180px;
  }

  .quick-actions {
    padding: 16px;
  }

  .action-icon {
    font-size: 20px;
  }

  .welcome-title {
    font-size: 20px;
  }

  .swipe-arrow {
    width: 32px;
    height: 32px;
  }

  .swipe-arrow-left {
    left: -16px;
  }

  .swipe-arrow-right {
    right: -16px;
  }

  .section-title {
    font-size: 16px;
  }

  .indicator-name {
    font-size: 14px;
  }

  .indicator-value {
    font-size: 15px;
  }
}

@media (max-width: 480px) {
  .main-container {
    padding: 10px;
  }

  .content-section {
    padding: 0 8px;
    padding-bottom: 100px;
  }

  .welcome-card {
    padding: 20px;
  }

  .overview-section {
    padding: 16px;
  }

  .section-title {
    font-size: 16px;
  }

  .swipe-content {
    /* padding: 12px; */
    min-height: 160px;
  }

  .indicator-row {
    padding: 12px;
  }

  .indicator-value {
    font-size: 14px;
  }

  .report-row {
    padding: 12px;
  }

  .swipe-arrow {
    width: 28px;
    height: 28px;
  }

  .swipe-arrow-left {
    left: -14px;
  }

  .swipe-arrow-right {
    right: -14px;
  }

  .swipe-arrow .van-icon {
    font-size: 14px;
  }

  .custom-indicators {
    margin-top: 12px;
  }

  .indicator-dot {
    width: 6px;
    height: 6px;
  }
}
</style>